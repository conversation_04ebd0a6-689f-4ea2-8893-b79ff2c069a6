import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.Test;

import com.tomsze.PythonFileParseUtils;

import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardOpenOption;
import java.io.IOException;

public class PythonFileParseUtilsTest {
    @Test
    void testReadPythonScriptSuccess() throws IOException {
        // Create a temporary file with content
        Path tempFile = Files.createTempFile("test_script", ".py");
        String expectedContent = "print('Hello, World!')";
        Files.write(tempFile, expectedContent.getBytes(), StandardOpenOption.WRITE);

        // Read the file using the function
        String actualContent = PythonFileParseUtils.readPythonScript(tempFile.toString());

        // Validate the content
        assertEquals(expectedContent, actualContent);

        // Clean up
        Files.delete(tempFile);
    }

    @Test
    void testReadPythonScriptFileNotFound() {
        // Attempt to read a nonexistent file
        String result = PythonFileParseUtils.readPythonScript("non_existent.py");

        // Expect null or exception handling
        assertNull(result);
    }

    @Test
    void testReadPythonScriptEmptyFile() throws IOException {
        // Create an empty temporary file
        Path tempFile = Files.createTempFile("empty_script", ".py");

        // Read the file using the function
        String result = PythonFileParseUtils.readPythonScript(tempFile.toString());

        // Expect an empty string
        assertEquals("", result);

        // Clean up
        Files.delete(tempFile);
    }
    // ----------------------------------------------

    @Test
    @SuppressWarnings("unchecked")
    public void testBasicConstantClass() {
        String script = "class Constants:\n" +
                        "    MAX_SIZE = 100\n" +
                        "    APP_NAME = \"MyApp\"\n" +
                        "    DEBUG_MODE = False\n";

        List<Map<String, Object>> result = PythonFileParseUtils.parseConstantClassesFromString(script);
        assertEquals(1, result.size());
        
        Map<String, Object> classInfo = result.get(0);
        assertEquals("Constants", classInfo.get("class"));
        
        Map<String, String> constants = (Map<String, String>) classInfo.get("constants");
        assertEquals("100", constants.get("MAX_SIZE"));
        assertEquals("\"MyApp\"", constants.get("APP_NAME"));
        assertEquals("False", constants.get("DEBUG_MODE"));
    }

    @Test
    @SuppressWarnings("unchecked")
    public void testClassWithComments() {
        String script = "class Settings:\n" +
                        "    TIMEOUT = 30  # seconds\n" +
                        "    API_KEY = \"abc123\"  # production key\n" +
                        "    ENABLED = True\n";

        List<Map<String, Object>> result = PythonFileParseUtils.parseConstantClassesFromString(script);
        assertEquals(1, result.size());
        
        Map<String, String> constants = (Map<String, String>) result.get(0).get("constants");
        assertEquals("30", constants.get("TIMEOUT"));
        assertEquals("\"abc123\"", constants.get("API_KEY"));
        assertEquals("True", constants.get("ENABLED"));
    }

    @Test
    @SuppressWarnings("unchecked")
    public void testMultiLineClassDeclaration() {
        String script = "class MultiLineClass(\n" +
                        "    BaseClass\n" +
                        "):\n" +
                        "    VALUE_A = 1\n" +
                        "    VALUE_B = 2\n";

        List<Map<String, Object>> result = PythonFileParseUtils.parseConstantClassesFromString(script);
        assertEquals(1, result.size());
        
        Map<String, String> constants = (Map<String, String>) result.get(0).get("constants");
        assertEquals("1", constants.get("VALUE_A"));
        assertEquals("2", constants.get("VALUE_B"));
    }

    @Test
    public void testIgnoreClassesWithMethods() {
        String script = "class ValidClass:\n" +
                        "    CONSTANT = 42\n\n" +
                        "class InvalidClass:\n" +
                        "    def method(self):\n" +
                        "        return None\n";

        List<Map<String, Object>> result = PythonFileParseUtils.parseConstantClassesFromString(script);
        assertEquals(1, result.size());
        assertEquals("ValidClass", result.get(0).get("class"));
    }

    @Test
    public void testEdgeCases() {
        // Empty class
        String script1 = "class EmptyClass:\n" +
                         "    pass\n";
        List<Map<String, Object>> result1 = PythonFileParseUtils.parseConstantClassesFromString(script1);
        assertTrue(result1.isEmpty());
    }

    @Test
    public void testFullExample() {
        String script = "import os\n" +
                        "from typing import Any, Dict, List\n" +
                        "from tomsze_utils import auth_utils\n" +
                        "\n" +
                        "class Constants:\n" +
                        "    MIN_PASSWORD_LENGTH = 8\n" +
                        "    REQUIRES_NUMBER = True\n" +
                        "    REQUIRES_SYMBOL = True\n" +
                        "    USER_DB_NAME = \"user_db\"\n" +
                        "    VERIFICATION_CODE_LENGTH = 6\n" +
                        "\n" +
                        "class SuccessMessages:\n" +
                        "    SIGN_UP = \"Sign up successful.\"\n" +
                        "    SIGN_IN = \"Sign in successful.\"\n" +
                        "\n" +
                        "class UserDatabase:\n" +
                        "    def __init__(self):\n" +
                        "        self.db = None\n" +
                        "\n" +
                        "class LoggedInStatus:\n" +
                        "    NOT_LOGGED_IN = \"not_logged_in\"\n" +
                        "    LOGGED_IN = \"logged_in\"\n";

        List<Map<String, Object>> result = PythonFileParseUtils.parseConstantClassesFromString(script);
        assertEquals(3, result.size());
        
        // Verify Constants
        Map<String, String> constants = findConstants(result, "Constants");
        assertEquals("8", constants.get("MIN_PASSWORD_LENGTH"));
        assertEquals("True", constants.get("REQUIRES_NUMBER"));
        assertEquals("\"user_db\"", constants.get("USER_DB_NAME"));
        
        // Verify SuccessMessages
        constants = findConstants(result, "SuccessMessages");
        assertEquals("\"Sign up successful.\"", constants.get("SIGN_UP"));
        
        // Verify LoggedInStatus
        constants = findConstants(result, "LoggedInStatus");
        assertEquals("\"not_logged_in\"", constants.get("NOT_LOGGED_IN"));
        
        // Ensure UserDatabase (with method) is excluded
        assertNull(findConstants(result, "UserDatabase"));
    }

    @SuppressWarnings("unchecked")
    private Map<String, String> findConstants(List<Map<String, Object>> classes, String className) {
        for (Map<String, Object> classInfo : classes) {
            if (className.equals(classInfo.get("class"))) {
                return (Map<String, String>) classInfo.get("constants");
            }
        }
        return null;
    }
}
