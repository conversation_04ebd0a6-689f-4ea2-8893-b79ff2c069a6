package com.tomsze.interface_example.interface_wrapped_example;

public abstract class GreetingInterfaceWrap implements GreetingInterface {
    @Override
    public final void greet() { // final so this method can't be overridden
        System.out.println("some text always print");
        greetImpl();
    }

    protected abstract void greetImpl(); // protected so only accessible from this package or
                                         // subclass
}
