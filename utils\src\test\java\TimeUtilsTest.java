
import java.time.Clock;
import java.time.Instant;
import java.time.ZoneId;

import static org.junit.jupiter.api.Assertions.assertEquals;
import org.junit.jupiter.api.Test;

import com.tomsze.TimeUtils;

public class TimeUtilsTest {

    @Test
    public void testGetClock_NullTimeAndZone() {
        Clock clock = TimeUtils.getClock(null, null);
        Instant expectedInstant = Instant.parse("2024-07-11T12:00:00Z");
        assertEquals(expectedInstant, clock.instant());
        assertEquals(ZoneId.of("UTC"), clock.getZone());
    }

    @Test
    public void testGetClock_ValidTimeAndZone() {
        String timeStr = "2024-07-11T15:00:00Z";
        ZoneId zone = ZoneId.of("America/New_York");
        Clock clock = TimeUtils.getClock(timeStr, zone);
        Instant expectedInstant = Instant.parse(timeStr);
        assertEquals(expectedInstant, clock.instant());
        assertEquals(zone, clock.getZone());
    }

    // ---------------------------------------------------

    @Test
    public void testConvertDateString_ValidDate() {
        String dateString = "2024-07-11";
        String customFormat = "yyyy/MM/dd";
        String expectedOutput = "2024/07/11";

        String result = TimeUtils.convertDateString(dateString, customFormat);
        assertEquals(expectedOutput, result);
    }

    @Test
    public void testConvertDateString_InvalidDate() {
        String dateString = "invalid-date";
        String customFormat = "yyyy/MM/dd";

        String result = TimeUtils.convertDateString(dateString, customFormat);
        assertEquals(null, result); // Expecting null for invalid date
    }

}
