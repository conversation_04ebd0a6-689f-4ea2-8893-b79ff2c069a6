package com.tomsze;

import org.json.JSONObject;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;

public class APIUtils {

    /**
     * Fetches the current time in the specified time zone using https://timeapi.io.
     *
     * @param timeZone (String): The time zone for which to fetch the current time.
     * @return result (JSONObject): A JSON object containing the current time information.
     * 
     * @example
     * 
     *          <pre>{@code
     * JSONObject timeInAmsterdam = APIUtils.apiGetCurrentTimeInZone("Europe/Amsterdam");
     * JSONObject timeInNewYork = APIUtils.apiGetCurrentTimeInZone("America/New_York");
     * }</pre>
     */
    public static JSONObject apiGetCurrentTimeInZone(String timeZone) throws Exception {
        String urlString = "https://timeapi.io/api/time/current/zone?timeZone=" + timeZone;
        // Use Jsoup to fetch and parse the response
        Document doc = Jsoup.connect(urlString)
                // .header("accept", "application/json")
                .ignoreContentType(true).timeout(20000).get();

        // Convert the response to a JSONObject
        return new JSONObject(doc.body().text());
    }

    /**
     * Fetches the response from the specified URL and converts it to a JSONObject.
     *
     * @param urlString (String): The URL from which to fetch the data.
     * @return result (JSONObject): A JSON object containing the parsed response data.
     * 
     * @example
     * 
     *          <pre>{@code
     * import com.tomsze.APIUtils;
     * 
     * JSONObject jsonResponse1 = APIUtils.apiGet("https://timeapi.io/api/time/current/zone?timeZone=Europe/Amsterdam");
     * JSONObject jsonResponse2 = APIUtils.apiGet("https://timeapi.io/api/time/current/zone?timeZone=America/New_York");
     * }</pre>
     */
    public static JSONObject apiGet(String urlString) throws Exception {
        // Use Jsoup to fetch and parse the response
        Document doc = Jsoup.connect(urlString)
                // .header("accept", "application/json")
                .ignoreContentType(true).timeout(5000).get();

        // Convert the response to a JSONObject
        return new JSONObject(doc.body().text());
    }


}
