
## To create a Jar file.
- Create a Manifest File
- Create a directory named META-INF in xxx/src/main/.
Inside this directory, create a file named MANIFEST.MF with the following content:
Manifest-Version: 1.0
Main-Class: com.example.Main
Specification-Title: Example Application
Specification-Version: 1.0
Specification-Vendor: Example Corp
Implementation-Title: Example Implementation
Implementation-Version: 1.0.0
Implementation-Vendor: Example Corp
- Package the JAR File (xx.jar will be created)
cd ./utils
mvn package

according to pom.xml:
<artifactId>utils</artifactId>
<version>1.15.2</version>
utils-1.15.2.jar will be created in target directory.

## To update version.
- Change the project -> version in pom.xml
- Change Implementation-Version in MANIFEST.MF

