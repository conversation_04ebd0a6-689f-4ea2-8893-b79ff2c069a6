package com.tomsze;

import java.util.*;
import java.util.regex.*;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;

public class PythonToJavaConverter {
    public static void main(String[] args) {
        // Print args
        System.out.println("Args: " + Arrays.toString(args));

        if (args.length != 4) {
            System.err.println("Usage: java PythonToJavaConverter '<python_script> <python_script>' <output_directory> <belong_to_package> <add_python_script_name_to_package_name>");
            return;
        }

        // log something to console so we know the jar is running.
        System.out.println("Hello, running PythonToJavaConverter!");

        List<String> pythonScript = Arrays.asList(args[0].split(","));
        String directoryPath = args[1];
        String belongToPackage = args[2];
        boolean addPythonScriptNameToPackageName = Boolean.parseBoolean(args[3]);

        try {
            convertPythonScriptsAndWriteToFile(pythonScript,  directoryPath, belongToPackage, addPythonScriptNameToPackageName);
        } catch (IOException e) {
            System.err.println("Error saving Java classes: " + e.getMessage());
            e.printStackTrace();
        }

        System.out.println("Hello, done running PythonToJavaConverter!");

    }

    // TODO: move methods to a separate utility class if needed.
    
    // Convert python script to Java String.
    public static String convertPythonScript(String pythonScript) {
        // Step 1: Parse the Python script to extract classes and their constants.
        Map<String, List<Constant>> classConstantsMap = parsePythonConstants(pythonScript);
        StringBuilder javaScript = new StringBuilder();
        
        // Step 2: Generate Java code based on the extracted classes and constants.
        for (String className : classConstantsMap.keySet()) {
            javaScript.append(generateJavaClassHeader(className));
            for (Constant constant : classConstantsMap.get(className)) {
                javaScript.append(generateJavaConstantDeclaration(constant));
            }
            javaScript.append(generateJavaClassFooter());
        }
        
        // Step 3: Return the assembled Java code.
        return javaScript.toString();
    }

    // Parses the provided Python script and returns a map from Python class name to the list of constants extracted from that class.
    private static Map<String, List<Constant>> parsePythonConstants(String pythonScript) {
        Map<String, List<Constant>> classConstantsMapping = new HashMap<>();
        List<ClassDefinition> classDefinitions = findClassesInPythonScript(pythonScript);
        
        for (ClassDefinition classDef : classDefinitions) {
            List<Constant> constants = new ArrayList<>();
            // Iterate over each line in the class body to identify constant definitions.
            for (String line : classDef.getBodyLines()) {
                if (isConstantDefinition(line)) {
                    String constantName = extractConstantName(line);
                    String constantValue = extractConstantValue(line);
                    String constantType = inferConstantType(constantValue);
                    constants.add(new Constant(constantName, constantValue, constantType));
                }
            }
            classConstantsMapping.put(classDef.getName(), constants);
        }
        return classConstantsMapping;
    }

    // Returns the header for a Java class given the class name.
    private static String generateJavaClassHeader(String className) {
        return "public class " + className + " {\n";
    }
    
    public static String generateJavaConstantDeclaration(Constant constant, int indentLevels) {
        String value = constant.getValue();
        if (constant.getType().equals("String")) {
            // Remove any existing quotes, then add standard double quotes.
            if ((value.startsWith("\"") && value.endsWith("\"")) ||
                (value.startsWith("'") && value.endsWith("'"))) {
                value = value.substring(1, value.length() - 1);
            }
            value = "\"" + value + "\"";
        }
        String indent = " ".repeat(indentLevels); // Each indent is 4 spaces.
        return indent + "public static final " + constant.getType() + " " 
            + constant.getName() + " = " + value + ";\n";
    }

    private static String generateJavaConstantDeclaration(Constant constant) {
        return generateJavaConstantDeclaration(constant, 2); // Default indentation of 2.
    }
    
    // Returns the Java class closing bracket.
    private static String generateJavaClassFooter() {
        return "}\n";
    }
    
    // Determines if a given line from a Python class body qualifies as a constant definition.
    // This implementation assumes that constants are defined with an uppercase identifier followed by '='.
    private static boolean isConstantDefinition(String line) {
        String trimmed = line.trim();
        // Matches lines like "NAME = value" or "NAME: type = value"
        // NAME: uppercase or underscore, optional type annotation, then '='
        return trimmed.matches("^[A-Z_][A-Z0-9_]*(\\s*:\\s*\\w+)?\\s*=\\s*.*");
    }
    
    // Extracts the constant name from a line formatted as "NAME = value".
    private static String extractConstantName(String line) {
        // Handles both "NAME = value" and "NAME : type = value"
        String[] parts = line.split("=", 2);
        String left = parts[0].trim();
        // If type annotation exists, split by ':'
        if (left.contains(":")) {
            return left.split(":", 2)[0].trim();
        }
        return left;
    }
    
    // Extracts the constant value from a line formatted as "NAME = value".
    private static String extractConstantValue(String line) {
        String[] parts = line.split("=", 2);
        return parts[1].trim();
    }
    
    // Infers the Java data type of a constant based on its value.
    // Uses simple heuristics: quoted values are considered strings; digits yield ints or doubles.
    private static String inferConstantType(String constantValue) {
        constantValue = constantValue.trim();
        
        // If the value is a quoted string.
        if ((constantValue.startsWith("\"") && constantValue.endsWith("\"")) ||
            (constantValue.startsWith("'") && constantValue.endsWith("'"))) {
            return "String";
        }
        // Check if the value is an integer.
        if (constantValue.matches("^-?\\d+$")) {
            return "int";
        }
        // Check if the value is a floating point number.
        if (constantValue.matches("^-?\\d*\\.\\d+$")) {
            return "double";
        }
        // Default: treat unknown formats as String.
        return "String";
    }
    
    // Scans the provided Python script for class definitions.
    // Assumes the class declaration starts with "class <Name>:" and that the class body consists of indented lines following the declaration.
    private static List<ClassDefinition> findClassesInPythonScript(String pythonScript) {
        List<ClassDefinition> classes = new ArrayList<>();
        String[] lines = pythonScript.split("\\r?\\n");
        ClassDefinition currentClass = null;
        Pattern classPattern = Pattern.compile("^class\\s+(\\w+)\\s*(?:\\([^)]*\\))?\\s*:");
        
        for (String line : lines) {
            Matcher matcher = classPattern.matcher(line);
            if (matcher.find()) {
                // A new class definition is found; create a new ClassDefinition.
                String className = matcher.group(1);
                currentClass = new ClassDefinition(className, new ArrayList<>());
                classes.add(currentClass);
            } else if (currentClass != null) {
                // If the line is indented, assume it belongs to the current class body.
                if (line.matches("^\\s+.*")) {
                    currentClass.addBodyLine(line.trim());
                } else {
                    // Encounter a non-indented line: current class body is finished.
                    currentClass = null;
                }
            }
        }
        return classes;
    }

    public static void convertPythonScriptAndWriteToFile(
            String pythonScriptPath,
            String directoryPath,
            String belongToPackage) throws IOException {
        convertPythonScriptAndWriteToFile(pythonScriptPath, directoryPath, belongToPackage, true);
    }

    public static void convertPythonScriptAndWriteToFile(
            String pythonScriptPath,
            String directoryPath,
            String belongToPackage,
            boolean addPythonScriptNameToPackageName) throws IOException {
        // Read the content of the Python script from the file at pythonScriptPath.
        String pythonScript = new String(Files.readAllBytes(Paths.get(pythonScriptPath)), StandardCharsets.UTF_8);

        // First, parse the Python script to get a mapping of class names to constants.
        Map<String, List<Constant>> classConstantsMap = parsePythonConstants(pythonScript);

        // Ensure that the directory exists; if not, create it.
        String newDirectoryPath = directoryPath;
        if (addPythonScriptNameToPackageName){
            newDirectoryPath = directoryPath + File.separator + new File(pythonScriptPath).getName().replace(".py", "");
        }
        File dir = new File(newDirectoryPath);
        if (!dir.exists()) {
            boolean created = dir.mkdirs();
            if (!created) {
                throw new IOException("Failed to create directory: " + newDirectoryPath);
            }
        }

        // Determine the package name to use
        String packageName = belongToPackage;
        if (addPythonScriptNameToPackageName && pythonScriptPath != null && !pythonScriptPath.isEmpty()) {
            String scriptFileName = new File(pythonScriptPath).getName();
            int dotIdx = scriptFileName.lastIndexOf('.');
            String scriptName = (dotIdx > 0) ? scriptFileName.substring(0, dotIdx) : scriptFileName;
            if (packageName != null && !packageName.trim().isEmpty()) {
                packageName = packageName.trim() + "." + scriptName;
            } else {
                packageName = scriptName;
            }
        }

        // Iterate over each class and generate its corresponding Java file.
        for (Map.Entry<String, List<Constant>> entry : classConstantsMap.entrySet()) {
            String className = entry.getKey();
            StringBuilder javaCode = new StringBuilder();

            // If packageName is non-empty, add the package statement at the top.
            if (packageName != null && !packageName.trim().isEmpty()) {
                javaCode.append("package ").append(packageName.trim()).append(";\n\n");
            }

            // Append the Java class header.
            javaCode.append(generateJavaClassHeader(className));
            // Append Java constant declarations.
            for (Constant constant : entry.getValue()) {
                javaCode.append(generateJavaConstantDeclaration(constant));
            }
            // Append the Java class footer.
            javaCode.append(generateJavaClassFooter());

            // Create a file for the class, e.g. "Constants.java"
            File outFile = new File(dir, className + ".java");
            try (FileWriter writer = new FileWriter(outFile)) {
                writer.write(javaCode.toString());
            }
        }
    }
    
    public static void convertPythonScriptsAndWriteToFile(List<String> pythonScriptPaths, 
                                                      String directoryPath, 
                                                      String belongToPackage,
                                                      boolean addPythonScriptNameToPackageName
                                                      ) throws IOException {
        // Ensure the directory exists just once.
        File dir = new File(directoryPath);
        if (!dir.exists()) {
            boolean created = dir.mkdirs();
            if (!created) {
                throw new IOException("Failed to create directory: " + directoryPath);
            }
        }

        // Process each Python script using the existing method.
        for (String pythonScriptPath : pythonScriptPaths) {
            // TODO: Optionally, you can remove the directory creation logic from the single-file method,
            // so you don't repeat it.
            convertPythonScriptAndWriteToFile(pythonScriptPath, directoryPath, belongToPackage, addPythonScriptNameToPackageName);
        }
    }
    
    
    // Inner class representing a Python class definition.
    private static class ClassDefinition {
        private final String name;
        private final List<String> bodyLines;
        
        public ClassDefinition(String name, List<String> bodyLines) {
            this.name = name;
            this.bodyLines = bodyLines;
        }
        
        public String getName() {
            return name;
        }
        
        public List<String> getBodyLines() {
            return bodyLines;
        }
        
        public void addBodyLine(String line) {
            bodyLines.add(line);
        }
    }
    
    // Inner class representing a constant definition (name, value, and inferred type).
    public static class Constant {
        private final String name;
        private final String value;
        private final String type;
        
        public Constant(String name, String value, String type) {
            this.name = name;
            this.value = value;
            this.type = type;
        }
        
        public String getName() {
            return name;
        }
        
        public String getValue() {
            return value;
        }
        
        public String getType() {
            return type;
        }
    }
}
