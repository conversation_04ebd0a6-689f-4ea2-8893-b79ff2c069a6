import java.time.Clock;
import java.time.Instant;
import java.time.ZoneId;
import java.util.Base64;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import com.macasaet.fernet.StringValidator;
import com.macasaet.fernet.Validator;
import com.tomsze.EncryptUtils;
import com.tomsze.JsonUtils;

public class EncryptUtilsTest {

    @Test
    public void testEncryptData() {
        String data_str = "Hello";
        String key_str = "01234567890123456789012345678901";
        byte[] dataBytes = data_str.getBytes();
        byte[] keyBytes = key_str.getBytes();

        String result = EncryptUtils.encryptData(dataBytes, keyBytes);
        assertNotEquals(data_str, result);
    }

    @Test
    public void testEncryptData_Twice() {
        String data_str = "Hello";
        String key_str = "01234567890123456789012345678901";
        byte[] dataBytes = data_str.getBytes();
        byte[] keyBytes = key_str.getBytes();

        String result = EncryptUtils.encryptData(dataBytes, keyBytes);
        String result2 = EncryptUtils.encryptData(dataBytes, keyBytes);
        assertNotEquals(result, result2);
    }


    @Test
    public void testEncryptData_InvalidKeyLength() {
        Exception exception = null;
        try {
            EncryptUtils.encryptData("hello world".getBytes(), "short_key".getBytes()); // Invalid
                                                                                        // key
                                                                                        // length
        } catch (IllegalArgumentException e) {
            exception = e;
        }
        assertEquals("Key must be an array of 32 bytes.",
                exception != null ? exception.getMessage() : null);
    }

    @Test
    public void testDecryptData() {
        String data_str = "Hello";
        String key_str = "01234567890123456789012345678901";
        byte[] dataBytes = data_str.getBytes();
        byte[] keyBytes = key_str.getBytes();

        String encryptedData = EncryptUtils.encryptData(dataBytes, keyBytes);
        String decryptedData = EncryptUtils.decryptData(keyBytes, encryptedData, null);

        assertEquals(data_str, decryptedData);
    }

    @Test
    public void testDecryptData_InvalidKeyLength() {
        Exception exception = null;
        String key_str = "short_key"; // Invalid key length
        String token_str = "some_token_string";

        try {
            EncryptUtils.decryptData(key_str.getBytes(), token_str, null);
        } catch (IllegalArgumentException e) {
            exception = e;
        }
        assertEquals("Key must be an array of 32 bytes.",
                exception != null ? exception.getMessage() : null);
    }

    @Test
    public void testDecryptData_KnownDataAndKey() {
        String key_str = "KkgIwvJmVA_pxButeRKUvDr0h8LwVQpAGq-Z5n8yiFk=";
        String token_str =
                "gAAAAABNLDrPt1baWNv81JU3EGVXECUdHYYvhxsslrYBewbC5zMoGiqb9OgJ-ML3b6iTDCesE2NpOqxYEVcMxq-4X3TVoVXarw==";

        int key_str_len = key_str.length();
        assertEquals(44, key_str_len);

        // Decode the key to a URL-safe base64 string
        byte[] keyBytes = Base64.getUrlDecoder().decode(key_str.getBytes());
        int keyBytes_len = keyBytes.length;
        assertEquals(32, keyBytes_len);

        Validator<String> validator = new StringValidator() {
            @Override
            public Clock getClock() {
                Instant fixedInstant = Instant.parse("2011-01-11T11:11:11Z");
                return Clock.fixed(fixedInstant, ZoneId.of("UTC"));
            }
        };


        String decryptedData = EncryptUtils.decryptData(keyBytes, token_str, validator);

        String expect_str = "Hello, World!";
        assertEquals(decryptedData, expect_str);
    }



    @Test
    public void testGenerateKeyFromString_ValidInput() throws Exception {
        String inputString = "myPassword";
        byte[] salt = "randomSalt".getBytes();
        Integer iterations = 100000;
        Integer length = 32;

        byte[] generatedKey =
                EncryptUtils.generateKeyFromString(inputString, salt, iterations, length);
        assertNotNull(generatedKey);
        Integer expectedLength = 44;
        assertEquals(expectedLength, generatedKey.length); // Check if the length is 44 bytes
    }

    @Test
    public void testGenerateKeyFromString_DefaultParameters() throws Exception {
        String inputString = "myPassword";

        byte[] generatedKey = EncryptUtils.generateKeyFromString(inputString, null, null, null);
        int expectedLength = 44;
        assertNotNull(generatedKey);
        assertEquals(expectedLength, generatedKey.length); // Check if the length is 44 bytes
    }

    // ---------------------------------------------------
    @Disabled
    @Test
    public void testGenerateKeyFromKeyDictAndApiTimezone_ValidInput() throws Exception {
        Map<String, String[]> dateStringMap =
                JsonUtils.loadJsonFromResourcesToMap("date_strings.json");

        byte[] generatedKey =
                EncryptUtils.generateKeyFromKeyDictAndApiTimezone(dateStringMap, null, null);
        assertNotNull(generatedKey);
        assertEquals(44, generatedKey.length); // Check if the length is 44 bytes
    }

    // ---------------------------------------------------
    @Disabled
    @Test
    public void generateKeyFromKeyDictAndServerDatetime_ValidInput() throws Exception {
        Map<String, String[]> dateStringMap =
                JsonUtils.loadJsonFromResourcesToMap("date_strings.json");

        String urlString = "http://192.168.128.80:8000/__whatappsdo/__request_datetime";
        byte[] generatedKey =
                EncryptUtils.generateKeyFromKeyDictAndServerDatetime(dateStringMap, null, urlString);
        assertNotNull(generatedKey);
        assertEquals(44, generatedKey.length); // Check if the length is 44 bytes
    }
}
