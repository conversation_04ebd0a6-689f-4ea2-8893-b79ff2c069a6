package com.tomsze;

import java.util.*;

import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.io.IOException;

public class PythonFileParseUtils {

    /**
     * Reads the content of a Python script file and returns it as a single string.
     * <p>
     * This method reads the entire content of the specified file using NIO's {@link Files#readString(Path)},
     * which ensures efficient reading while preserving the original formatting and indentation.
     * If the file cannot be read due to an I/O error, the exception is caught and printed,
     * and null is returned.
     * </p>
     *
     * @param filePath (String): The path to the Python script file to be read.
     * @return String: The content of the Python script as a single string, or null if reading fails.
     *
     * @example
     * <pre>{@code
     * import java.util.List;
     * import java.util.Map;
     * 
     * String scriptContent = PythonFileParseUtils.readPythonScript(filePath: "path/to/script.py");
     * List<Map<String, Object>> constants = PythonFileParseUtils.parseConstantClassesFromString(pythonScriptContent: scriptContent);
     * }</pre>
     *
     * @example
     * <pre>{@code
     * import java.util.List;
     * import java.util.Map;
     * 
     * String invalidContent = PythonFileParseUtils.readPythonScript(filePath: "nonexistent.py");
     * assert invalidContent == null;  // Reading non-existent file returns null.
     * }</pre>
     */
    public static String readPythonScript(String filePath) {
        try {
            Path path = Paths.get(filePath);
            return Files.readString(path);
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * Parses Python script content and extracts classes that define only constants (class-level assignments).
     * <p>
     * This method scans the provided Python script content, identifies class declarations, and collects
     * all assignments at the class body level (excluding methods, decorators, and nested classes).
     * Only classes that contain at least one constant and do not contain methods or nested classes are included.
     * Multi-line class headers and inline comments are supported.
     * </p>
     *
     * @param pythonScriptContent (String): The content of a Python script as a single string.
     * @return result (List&lt;Map&lt;String, Object&gt;&gt;): A list of maps, each representing a class with its name ("class")
     *         and a map of constant assignments ("constants").
     *
     * @example
     * <pre>{@code
     * import java.util.List;
     * import java.util.Map;
     * 
     * String script = ""
     *     + "class Colors:\n"
     *     + "    RED = '#ff0000'\n"
     *     + "    GREEN = '#00ff00'\n"
     *     + "\n"
     *     + "class Empty:\n"
     *     + "    pass\n";
     * List<Map<String, Object>> result = PythonFileParseUtils.parseConstantClassesFromString(pythonScriptContent: script);
     * // result contains one entry for "Colors" with constants RED and GREEN.
     * }</pre>
     *
     * @example
     * <pre>{@code
     * import java.util.List;
     * import java.util.Map;
     * 
     * String script = ""
     *     + "class Config(\n"
     *     + "    object\n"
     *     + "):\n"
     *     + "    HOST = 'localhost'  # The host\n"
     *     + "    PORT = 8080\n"
     *     + "\n"
     *     + "class NotAConstant:\n"
     *     + "    def foo(self):\n"
     *     + "        pass\n";
     * List<Map<String, Object>> result = PythonFileParseUtils.parseConstantClassesFromString(pythonScriptContent: script);
     * // result contains one entry for "Config" with constants HOST and PORT.
     * }</pre>
     */
    public static List<Map<String, Object>> parseConstantClassesFromString(String pythonScriptContent) {
        String[] lines = pythonScriptContent.split("\n");
        List<Map<String, Object>> result = new ArrayList<>();

        // State for the class declaration.
        String currentClassName = null;
        Map<String, String> currentConstants = null;
        boolean classIsValid = true;

        // State for collecting class header spanning multiple lines.
        boolean collectingHeader = false;
        StringBuilder headerBuilder = new StringBuilder();

        // State for processing class body.
        boolean inBody = false;
        int bodyIndent = -1;

        for (int i = 0; i < lines.length; i++) {
            String line = lines[i];
            int indent = countLeadingSpaces(line);
            String trimmed = line.trim();

            // If we are collecting header lines from a multi-line declaration.
            if (collectingHeader) {
                headerBuilder.append(" ").append(trimmed);
                if (trimmed.endsWith(":")) {
                    // Header complete; extract the class name.
                    currentClassName = extractClassNameFromHeader(headerBuilder.toString());
                    collectingHeader = false;
                    inBody = false;
                    bodyIndent = -1;
                    currentConstants = new HashMap<>();
                    classIsValid = true;
                }
                continue;
            }

            // If no current class is being processed, look for a class start.
            if (currentClassName == null) {
                if (indent == 0 && trimmed.startsWith("class ")) {
                    // Begin collecting the header.
                    collectingHeader = true;
                    headerBuilder.setLength(0);
                    headerBuilder.append(trimmed);
                    if (trimmed.endsWith(":")) {  // Single line header.
                        currentClassName = extractClassNameFromHeader(headerBuilder.toString());
                        collectingHeader = false;
                        inBody = false;
                        bodyIndent = -1;
                        currentConstants = new HashMap<>();
                        classIsValid = true;
                    }
                }
                continue;
            } else {
                // We have a class header; now we are either waiting for or processing the body.
                if (!inBody) {
                    // Waiting for the first non-empty line that is part of the body.
                    if (trimmed.isEmpty()) {
                        continue;
                    }
                    if (indent > 0) {
                        inBody = true;
                        bodyIndent = indent;
                    } else {
                        // A new zero-indented line means this class got no body.
                        if (classIsValid && currentConstants != null && !currentConstants.isEmpty()) {
                            Map<String, Object> classInfo = new HashMap<>();
                            classInfo.put("class", currentClassName);
                            classInfo.put("constants", currentConstants);
                            result.add(classInfo);
                        }
                        // Restart: reset state then check if a new class starts on this line.
                        currentClassName = null;
                        inBody = false;
                        bodyIndent = -1;
                        if (indent == 0 && trimmed.startsWith("class ")) {
                            collectingHeader = true;
                            headerBuilder.setLength(0);
                            headerBuilder.append(trimmed);
                            if (trimmed.endsWith(":")) {
                                currentClassName = extractClassNameFromHeader(headerBuilder.toString());
                                collectingHeader = false;
                                inBody = false;
                                bodyIndent = -1;
                                currentConstants = new HashMap<>();
                                classIsValid = true;
                            }
                        }
                        continue;
                    }
                }

                // Now, we are inside the class body.
                if (indent < bodyIndent) {
                    // The class body ended.
                    if (classIsValid && currentConstants != null && !currentConstants.isEmpty()) {
                        Map<String, Object> classInfo = new HashMap<>();
                        classInfo.put("class", currentClassName);
                        classInfo.put("constants", currentConstants);
                        result.add(classInfo);
                    }
                    // Reset state and reprocess this line.
                    currentClassName = null;
                    inBody = false;
                    bodyIndent = -1;
                    if (indent == 0 && trimmed.startsWith("class ")) {
                        collectingHeader = true;
                        headerBuilder.setLength(0);
                        headerBuilder.append(trimmed);
                        if (trimmed.endsWith(":")) {
                            currentClassName = extractClassNameFromHeader(headerBuilder.toString());
                            collectingHeader = false;
                            inBody = false;
                            bodyIndent = -1;
                            currentConstants = new HashMap<>();
                            classIsValid = true;
                        }
                    }
                    continue;
                } else if (indent > bodyIndent) {
                    // Nested indentation (such as a method) invalidates the class.
                    classIsValid = false;
                } else { // indent == bodyIndent
                    if (trimmed.startsWith("def ") || trimmed.startsWith("class ") || trimmed.startsWith("@")) {
                        classIsValid = false;
                    } else if (trimmed.contains("=")) {
                        String[] parts = trimmed.split("\\s*=\\s*", 2);
                        if (parts.length == 2) {
                            String varName = parts[0].trim();
                            String value = parts[1].trim();
                            // Remove inline comments.
                            if (value.contains("#")) {
                                value = value.split("#")[0].trim();
                            }
                            currentConstants.put(varName, value);
                        }
                    }
                }
            }
        }

        // End-of-file: if there's an unfinished class, finalize it.
        if (currentClassName != null && inBody && classIsValid && currentConstants != null && !currentConstants.isEmpty()) {
            Map<String, Object> classInfo = new HashMap<>();
            classInfo.put("class", currentClassName);
            classInfo.put("constants", currentConstants);
            result.add(classInfo);
        }

        return result;
    }

    private static int countLeadingSpaces(String line) {
        int count = 0;
        for (int i = 0; i < line.length(); i++) {
            if (line.charAt(i) == ' ') {
                count++;
            } else {
                break;
            }
        }
        return count;
    }

    private static String extractClassNameFromHeader(String header) {
        // Remove the "class " prefix.
        String s = header.substring(5).trim();
        int idx = s.indexOf("(");
        if (idx != -1) {
            s = s.substring(0, idx).trim();
        } else if (s.endsWith(":")) {
            s = s.substring(0, s.length() - 1).trim();
        }
        return s;
    }
}
