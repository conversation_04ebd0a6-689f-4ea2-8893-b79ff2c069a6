import java.io.IOException;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.json.JSONObject;
import org.junit.jupiter.api.Test;

import com.tomsze.JsonUtils;
import java.util.Arrays;
import java.util.List;

public class JsonUtilsTest {

    @Test
    public void testReadJsonString_ValidJson() throws Exception {
        String jsonString = "{\"key1\": [\"value1\", \"value2\"], \"key2\": [\"value3\"]}";
        Map<String, String[]> result = JsonUtils.readJsonString(jsonString);

        assertNotNull(result);
        assertTrue(result.containsKey("key1"));
        assertTrue(result.containsKey("key2"));
        assertEquals(2, result.get("key1").length);
        assertEquals(1, result.get("key2").length);
    }

    @Test
    public void testReadJsonString_InvalidJson() {
        String invalidJsonString = "{\"key1\": [\"value1\", \"value2\", \"key2\": [\"value3\"]}"; // Missing
                                                                                                  // brace
        Exception exception = null;

        try {
            JsonUtils.readJsonString(invalidJsonString);
        } catch (com.google.gson.JsonSyntaxException e) {
            exception = e;
        }

        assertNotNull(exception);
    }

    // --------------------------------------------------
    @Test
    public void testLoadJsonFromResources_ValidFile() throws Exception {
        String jsonString = JsonUtils.loadJsonFromResources("date_strings.json");
        assertNotNull(jsonString);
        assertTrue(jsonString.contains("0104")); // Replace with actual expected content
    }

    @Test
    public void testLoadJsonFromResources_InvalidFile() throws IOException {
        Exception exception = null;

        try {
            JsonUtils.loadJsonFromResources("invalidFile.json");
        } catch (IllegalArgumentException e) {
            exception = e;
        }

        assertNotNull(exception);
    }

    // --------------------------------------------------
    @Test
    public void testLoadJsonFromResourcesToMap_ValidFile() throws Exception {
        Map<String, String[]> result = JsonUtils.loadJsonFromResourcesToMap("date_strings.json");
        assertNotNull(result);
        assertTrue(result.containsKey("0104")); // Replace with actual expected key
        assertEquals(2, result.get("0104").length); // Replace with actual expected length
    }

    @Test
    public void testLoadJsonFromResourcesToMap_InvalidFile() throws IOException {
        Exception exception = null;

        try {
            JsonUtils.loadJsonFromResourcesToMap("invalidFile.json");
        } catch (IllegalArgumentException e) {
            exception = e;
        }

        assertNotNull(exception);
    }

    // --------------------------------------------------
    @Test
    public void testConstructNestedJson_MultiLevelCreation() throws Exception {
        JSONObject root = new JSONObject();

        List<String> keys = Arrays.asList("user", "details");
        String valueKey = "age";
        int value = 30;

        JSONObject result = JsonUtils.constructNestedJson(root, keys, valueKey, value, true);

        // Validate structure
        assertTrue(root.has("user"));
        JSONObject userObj = root.getJSONObject("user");
        assertTrue(userObj.has("details"));
        JSONObject detailsObj = userObj.getJSONObject("details");
        assertTrue(detailsObj.has(valueKey));
        assertEquals(value, detailsObj.getInt(valueKey));

        // Validate return value
        assertEquals(detailsObj, result);
    }

    @Test
    public void testConstructNestedJson_ExistingIntermediateKeys() throws Exception {
        JSONObject root = new JSONObject();

        // Pre-populate root with existing structure
        JSONObject userObj = new JSONObject();
        userObj.put("address", "123 Main St");
        root.put("user", userObj);

        List<String> keys = Arrays.asList("user", "details");
        String valueKey = "age";
        int value = 30;

        JSONObject result = JsonUtils.constructNestedJson(root, keys, valueKey, value, true);

        // Validate existing "address" is preserved
        JSONObject user = root.getJSONObject("user");
        assertEquals("123 Main St", user.getString("address"));

        // Validate new "details.age" is added
        JSONObject details = user.getJSONObject("details");
        assertEquals(value, details.getInt(valueKey));

        // Validate return value
        assertEquals(details, result);
    }
    
    @Test
    public void testConstructNestedJson_SingleLevel() throws Exception {
        JSONObject root = new JSONObject();

        List<String> keys = Arrays.asList("preferences");
        String valueKey = "notifications";
        boolean value = true;

        JSONObject result = JsonUtils.constructNestedJson(root, keys, valueKey, value, true);

        // Validate structure
        assertTrue(root.has("preferences"));
        JSONObject preferences = root.getJSONObject("preferences");
        assertTrue(preferences.has(valueKey));
        assertEquals(value, preferences.getBoolean(valueKey));

        // Validate return value
        assertEquals(preferences, result);
    }

    @Test
    public void testConstructNestedJson_EmptyKeys() {
        JSONObject root = new JSONObject();
        List<String> keys = List.of(); // Empty list
        String valueKey = "key";
        String value = "value";

        assertThrows(ArrayIndexOutOfBoundsException.class, () -> {
            JsonUtils.constructNestedJson(root, keys, valueKey, value, true);
        });
    }

    @Test
    public void testConstructNestedJson_NotModifyingOriginal() throws Exception {
        JSONObject root = new JSONObject();
        root.put("existingKey", "existingValue");
        
        List<String> keys = Arrays.asList("user", "details");
        String valueKey = "age";
        int value = 30;

        JSONObject result = JsonUtils.constructNestedJson(root, keys, valueKey, value, false);

        // Verify original object is unchanged
        assertEquals(1, root.length());
        assertTrue(root.has("existingKey"));
        assertEquals("existingValue", root.getString("existingKey")); 
        assertFalse(root.has("user"));

        // Verify result has correct nested structure
        assertTrue(result.has(valueKey));
        assertEquals(value, result.getInt(valueKey));
    }


}
