
package com.tomsze;

import java.time.Clock;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;

public class TimeUtils {


    /**
     * Returns a Clock instance set to a fixed instant based on the provided time string. If the
     * time string is null, a default value is used.
     *
     * @param time_str (String): The time string in ISO-8601 format.
     * @param zone (ZoneId): The time zone to be used. Defaults to UTC if null.
     * @return clock (Clock): A Clock instance set to the specified time.
     * 
     * @example
     * 
     *          <pre>{@code
     * import java.time.Clock;
     * import java.time.ZoneId;
     * 
     * Clock clock1 = TimeUtils.getClock(null, null); // Uses default time and zone
     * Clock clock2 = TimeUtils.getClock("2024-07-11T15:00:00Z", ZoneId.of("America/New_York")); // Uses provided time and zone
     * }</pre>
     */
    public static Clock getClock(String time_str, ZoneId zone) {
        if (time_str == null)
            time_str = "2024-07-11T12:00:00Z";
        if (zone == null)
            zone = ZoneId.of("UTC");

        Instant fixedInstant = Instant.parse(time_str);
        return Clock.fixed(fixedInstant, zone);
    }

    /**
     * Converts a date string from "yyyy-MM-dd" format to a custom format.
     *
     * @param dateString (String): The date string in "yyyy-MM-dd" format.
     * @param customFormat (String): The custom format to convert the date to. Defaults to
     *        "##yyyy~~MM--dd**" if null.
     * @return formattedDate (String): The date formatted according to the custom format.
     * 
     * @example
     * 
     *          <pre>{@code
     * import com.tomsze.TimeUtils;
     * 
     * String result1 = TimeUtils.convertDateString("2024-07-11", "yyyy/MM/dd"); // Returns "2024/07/11"
     * String result2 = TimeUtils.convertDateString("2024-07-11", null); // Returns "##2024~~07--11**"
     * }</pre>
     */
    public static String convertDateString(String dateString, String customFormat) {
        if (customFormat == null) {
            customFormat = "##yyyy~~MM--dd**";
        }

        // Define the input date format
        DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        try {
            // Parse the input date string to LocalDate
            LocalDate date = LocalDate.parse(dateString, inputFormatter);

            // Replace the components with actual values
            String formattedDate = customFormat.replace("yyyy", String.valueOf(date.getYear()))
                    .replace("MM", String.format("%02d", date.getMonthValue())) // Ensure two digits
                                                                                // for month
                    .replace("dd", String.format("%02d", date.getDayOfMonth())); // Ensure two
                                                                                 // digits for day

            return formattedDate;
        } catch (DateTimeParseException e) {
            System.err.println("Invalid date format: " + e.getMessage());
            return null; // or throw an exception, or return an error message
        }
    }

}
