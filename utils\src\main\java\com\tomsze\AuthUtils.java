package com.tomsze;

import org.mindrot.jbcrypt.BCrypt;

public class AuthUtils {

    /**
     * Hashes a password using bcrypt.
     *
     * @param password (String): The password to be hashed.
     * @return hashedPassword (String): The hashed password.
     * 
     * @example
     * 
     *          <pre>{@code
     * import com.tomsze.AuthUtils;
     * 
     * String hashedPassword1 = AuthUtils.hashPassword("my_secure_password");
     * String hashedPassword2 = AuthUtils.hashPassword("another_password");
     * }</pre>
     * 
     * @example
     * 
     *          <pre>{@code
     * import com.tomsze.AuthUtils;
     * 
     * String hashedPassword = AuthUtils.hashPassword("password123");
     * String hashedPasswordWithSpecialChars = AuthUtils.hashPassword("p@ssw0rd!");
     * }</pre>
     */
    public static String hashPassword(String password) {
        // Generate a salt
        String salt = BCrypt.gensalt();
        // Hash the password with the generated salt
        String hashedPassword = BCrypt.hashpw(password, salt);
        return hashedPassword;
    }


    /**
     * Verifies a password against a hashed password using bcrypt.
     *
     * @param hashedPassword (String): The hashed password to verify against.
     * @param password (String): The password to be verified.
     * @return result (boolean): True if the password matches the hashed password, false otherwise.
     * 
     * @example
     * 
     *          <pre>{@code
     * import com.tomsze.AuthUtils;
     * 
     * String hashedPassword = AuthUtils.hashPassword("my_secure_password");
     * boolean isVerified = AuthUtils.verifyPassword(hashedPassword, "my_secure_password"); // should return true
     * boolean isNotVerified = AuthUtils.verifyPassword(hashedPassword, "wrong_password"); // should return false
     * }</pre>
     */
    public static boolean verifyPassword(String hashedPassword, String password) {
        return BCrypt.checkpw(password, hashedPassword);
    }

    public static void main(String[] args) {
        // Examples of hashing passwords
        String hashed1 = hashPassword("my_secure_password");
        System.out.println("Hashed password 1: " + hashed1);

        String hashed2 = hashPassword("another_password");
        System.out.println("Hashed password 2: " + hashed2);

        boolean is_verified = verifyPassword(hashed1, hashed2);
        System.out.println("is_verified: " + is_verified);

        boolean is_verified2 = verifyPassword(hashed1, "my_secure_password");
        System.out.println("is_verified2: " + is_verified2);
    }
}
