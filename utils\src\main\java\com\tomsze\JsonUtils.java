package com.tomsze;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.StringReader;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.google.gson.stream.JsonReader;

import org.json.JSONException;
import org.json.JSONObject;

public class JsonUtils {


    /**
     * Reads a JSON string and converts it into a Map with String keys and String array values.
     *
     * @param jsonString (String): The JSON string to be parsed.
     * @return (Map<String, String[]>): A map containing the parsed JSON data.
     * 
     * @example
     * 
     *          <pre>{@code
     * import com.tomsze.JavaUtils;
     * 
     * Map<String, String[]> result = JavaUtils.readJsonString("{\"key1\": [\"value1\", \"value2\"], \"key2\": [\"value3\"]}");
     * }</pre>
     * 
     * @example
     * 
     *          <pre>{@code
     * import com.tomsze.JavaUtils;
     * 
     * String jsonString = "{\"name\": [\"Alice\", \"Bob\"], \"age\": [\"30\"]}";
     * Map<String, String[]> result = JavaUtils.readJsonString(jsonString);
     * }</pre>
     */
    public static Map<String, String[]> readJsonString(String jsonString) {
        return new Gson().fromJson(new JsonReader(new StringReader(jsonString)),
                new TypeToken<Map<String, String[]>>() {}.getType());
    }

    /**
     * Loads a JSON file from the resources and returns its content as a String.
     *
     * @param fileName (String): The name of the JSON file to be loaded.
     * @return jsonString (String): The content of the JSON file as a String.
     * 
     * @example
     * 
     *          <pre>{@code
     * import com.tomsze.JavaUtils;
     * 
     * String jsonContent = JavaUtils.loadJsonFromResources("date_strings.json");
     * }</pre>
     * 
     * @example
     * 
     *          <pre>{@code
     * import com.tomsze.JavaUtils;
     * 
     * String jsonContent = JavaUtils.loadJsonFromResources("another_file.json");
     * }</pre>
     */
    public static String loadJsonFromResources(String fileName) throws IOException {
        // Use the class loader to get the resource as an InputStream
        InputStream inputStream = JsonUtils.class.getClassLoader().getResourceAsStream(fileName);

        if (inputStream == null) {
            throw new IllegalArgumentException("File not found: " + fileName);
        }

        // Read the InputStream into a String
        StringBuilder jsonString = new StringBuilder();
        try (BufferedReader reader =
                new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {
            String line;
            while ((line = reader.readLine()) != null) {
                jsonString.append(line);
            }
        }

        return jsonString.toString();
    }

    /**
     * Loads a JSON file from the resources and converts its content to a Map.
     *
     * @param fileName (String): The name of the JSON file to be loaded.
     * @return result (Map<String, String[]>): A map representation of the JSON file content.
     * 
     * @example
     * 
     *          <pre>{@code
     * import com.tomsze.JsonUtils;
     * 
     * Map<String, String[]> result = JsonUtils.loadJsonFromResourcesToMap(fileName: "date_strings.json");
     * }</pre>
     * 
     * @example
     * 
     *          <pre>{@code
     * import com.tomsze.JsonUtils;
     * 
     * Map<String, String[]> result = JsonUtils.loadJsonFromResourcesToMap(fileName: "another_file.json");
     * }</pre>
     */
    public static Map<String, String[]> loadJsonFromResourcesToMap(String fileName)
            throws IOException {
        // Use the class loader to get the resource as an InputStream
        InputStream inputStream = JsonUtils.class.getClassLoader().getResourceAsStream(fileName);

        if (inputStream == null) {
            throw new IllegalArgumentException("File not found: " + fileName);
        }

        // Read the InputStream into a String
        StringBuilder jsonString = new StringBuilder();
        try (BufferedReader reader =
                new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {
            String line;
            while ((line = reader.readLine()) != null) {
                jsonString.append(line);
            }
        }

        // Convert the JSON string to a Map and return it
        return new Gson().fromJson(jsonString.toString(),
                new TypeToken<Map<String, String[]>>() {}.getType());
    }

    /**
     * Constructs or extends a nested JSON structure within the provided {@code jsonObject} based on a list of keys,
     * and sets a value under a specified key at the deepest level.
     *
     * @param jsonObject The root JSON object to build the structure upon. May be modified if {@code modifyOriginal} is {@code true}.
     * @param keys A non-empty list of keys representing the path to the target nested location.
     * @param valueKey The key under which the final value will be placed at the deepest level.
     * @param value The value to be placed at the deepest level; can be any valid JSON type (e.g., {@link String}, {@link Number}, {@link Boolean}, {@link JSONObject}).
     * @param modifyOriginal If {@code true}, modifies the original {@code jsonObject} in place. If {@code false}, creates and modifies a deep copy, leaving the original unchanged.
     * 
     * @return The deepest nested {@code JSONObject} created or found at the end of the keys path, containing the {@code valueKey} and {@code value}.
     *
     * @throws ArrayIndexOutOfBoundsException if the {@code keys} list is empty.
     * @throws JSONException if a key is invalid or the operation fails.
     *
     * @example
     * <pre>{@code
     * import org.json.JSONObject;
     * import java.util.Arrays;
     * import java.util.List;
     * 
     * JSONObject root = new JSONObject();
     * List<String> keys = Arrays.asList("user", "details");
     * JSONObject result = JsonUtils.constructNestedJson(root, keys, "age", 30, false); // Integer value
     * }</pre>
     *
     * @example
     * <pre>{@code
     * import org.json.JSONObject;
     * import java.util.Arrays;
     * import java.util.List;
     * 
     * JSONObject root = new JSONObject();
     * List<String> keys = Arrays.asList("preferences");
     * JSONObject result = JsonUtils.constructNestedJson(root, keys, "notifications", true, true); // Boolean value
     * }</pre>
     *
     * @note If {@code modifyOriginal} is {@code true}, the original {@code jsonObject} is modified and the returned object is a reference to the nested object within it. If {@code false}, the original is not changed.
     */
    public static JSONObject constructNestedJson(
        JSONObject jsonObject,
        List<String> keys,
        String valueKey,
        Object value,
        boolean modifyOriginal)
        throws JSONException {

        if (keys.isEmpty()) {
            throw new ArrayIndexOutOfBoundsException("Keys list cannot be empty");
        }

        JSONObject startingJson = modifyOriginal 
            ? jsonObject 
            : new JSONObject(jsonObject.toString());
        JSONObject currentJson = startingJson;

        for (String key : keys) {
            if (!currentJson.has(key)) {
                currentJson.put(key, new JSONObject());
            }
            currentJson = currentJson.getJSONObject(key);
        }

        currentJson.put(valueKey, value);

        return currentJson; // Return the last nested object
    }


}
