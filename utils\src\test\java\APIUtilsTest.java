
import org.json.JSONObject;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Disabled;

import com.tomsze.APIUtils;

public class APIUtilsTest {

    @Disabled
    @Test
    public void testApiGetCurrentTimeInZone() throws Exception {
        // String urlString = "http://192.168.128.80:8000/";
        String timeZone = "Europe/Amsterdam";
        JSONObject resultJSONObject = APIUtils.apiGetCurrentTimeInZone(timeZone);

        // Check that the result is not null and contains expected keys
        assertNotNull(resultJSONObject);
        assertTrue(resultJSONObject.has("dateTime"));
    }

    // -------------------------------------------------------
    @Disabled
    @Test
    public void testApiGet() throws Exception {
        // String urlString = "http://192.168.128.80:8000/";
        String urlString = "https://timeapi.io/api/time/current/zone?timeZone=Europe/Amsterdam";
        JSONObject resultJSONObject = APIUtils.apiGet(urlString);

        // Check that the result is not null and contains expected keys
        assertNotNull(resultJSONObject);
        assertTrue(resultJSONObject.has("dateTime"));
    }
}
