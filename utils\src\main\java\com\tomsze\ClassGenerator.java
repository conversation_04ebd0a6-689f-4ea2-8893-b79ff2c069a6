package com.tomsze;
import java.io.FileWriter;
import java.io.IOException;
import java.util.List;
import java.util.Map;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.util.stream.Collectors;

public class ClassGenerator {
    /**
     * Generates a Java class file based on the provided list of elements.
     *
     * @param elements (List<Map<String, Object>>): A list of maps where each map represents a class element.
     * - Each map can contain the following keys:
     *   - "class": Specifies the class name. If not provided, "DefaultClass" will be used as the default name.
     *   - "constants": A map of constant names and values to be included in the class.
     * 
     * @param numberIndentSpace (int): The number of spaces to use for indentation in the generated class file.
     *
     * @return void: This function does not return any value. It generates a Java file directly.
     *
     * @example
     * <pre>{@code
     * // Example 1: Generate a class with default name and constants
     * List<Map<String, Object>> elements = new ArrayList<>();
     * Map<String, Object> constants = new HashMap<>();
     * constants.put("VERSION", "1.0");
     * constants.put("DEBUG_MODE", true);
     * elements.add(Map.of("constants", constants));
     * ClassGenerator.generateClassFile(elements, 4); // 4 spaces for indentation
     * // Resulting class will be named "DefaultClass" with two public static final constants.
     * }</pre>
     *
     * @example
     * <pre>{@code
     * // Example 2: Generate a custom-named class with constants
     * List<Map<String, Object>> elements = new ArrayList<>();
     * elements.add(Map.of("class", "AppConfig"));
     * Map<String, Object> constants = new HashMap<>();
     * constants.put("MAX_RETRIES", 3);
     * constants.put("APP_NAME", "MyApp");
     * elements.add(Map.of("constants", constants));
     * ClassGenerator.generateClassFile(elements, 4); // 4 spaces for indentation
     * // Resulting class will be named "AppConfig" with two public static final constants.
     * }</pre>
     *
     * @imports
     * java.util.List, java.util.Map, java.util.ArrayList, java.util.HashMap
     */
    public static void generateClassFile(List<Map<String, Object>> elements, int numberIndentSpace) {
        String className = "DefaultClass"; // Default class name
        StringBuilder classContent = new StringBuilder();

        // Generate the indent string based on numberIndentSpace
        String indent = " ".repeat(numberIndentSpace);

        // Start parsing the input list
        for (Map<String, Object> element : elements) {
            if (element.containsKey("class")) {
                className = element.get("class").toString();
            }
            if (element.containsKey("constants")) {
                @SuppressWarnings("unchecked")
                Map<String, Object> constants = (Map<String, Object>) element.get("constants");
                for (Map.Entry<String, Object> entry : constants.entrySet()) {
                    // Determine the type based on the value instance
                    String type = entry.getValue() instanceof String ? "String" : "boolean";
                    classContent.append(indent)
                                .append("public static final ")
                                .append(type)
                                .append(" ")
                                .append(entry.getKey())
                                .append(" = ")
                                .append(type.equals("String") ? "\"" + entry.getValue() + "\"" : entry.getValue())
                                .append(";\n");
                }
            }
        }

        // Build the class file content
        String classTemplate = "public class " + className + " {\n" + 
                            classContent.toString() + "}\n";

        // Write to a file
        try (FileWriter writer = new FileWriter(className + ".java")) {
            writer.write(classTemplate);
            System.out.println("Class file '" + className + ".java' generated successfully.");
        } catch (IOException e) {
            e.printStackTrace();
        }
    }


    /**
     * Generates a Java enum source file based on the input text file.
     *
     * @param inputFile   the text file containing one enum constant per line.
     * @param outputDir   the output directory to write the generated Java file.
     * @param packageName the package for the generated enum.
     * @param enumName    the name of the enum class.
     * @throws IOException if reading or writing files fails.
     */
    public static void generateEnum(File inputFile, File outputDir, String packageName, String enumName) throws IOException {
        if (!outputDir.exists() && !outputDir.mkdirs()) {
            throw new IOException("Failed to create output directory: " + outputDir.getAbsolutePath());
        }

        // Read nonempty, trimmed lines from the input file.
        List<String> enumValues = Files.readAllLines(inputFile.toPath(), StandardCharsets.UTF_8)
            .stream()
            .map(String::trim)
            .filter(s -> !s.isEmpty())
            .collect(Collectors.toList());

        // Build the enum source.
        StringBuilder enumSource = new StringBuilder();
        enumSource.append("package ").append(packageName).append(";\n\n");
        enumSource.append("public enum ").append(enumName).append(" {\n");
        for (int i = 0; i < enumValues.size(); i++) {
            String constant = enumValues.get(i).toUpperCase();
            enumSource.append("    ").append(constant);
            if (i < enumValues.size() - 1) {
                enumSource.append(",\n");
            } else {
                enumSource.append(";\n");
            }
        }
        enumSource.append("}\n");

        // Write out the generated source file.
        File outputFile = new File(outputDir, enumName + ".java");
        Files.write(outputFile.toPath(), enumSource.toString().getBytes(StandardCharsets.UTF_8));
        System.out.println("Generated enum file at: " + outputFile.getAbsolutePath());
    }

    /**
     * Generates Java class content based on the provided list of elements.
     *
     * @param elements (List<Map<String, Object>>): A list of maps where each map represents a class element.
     *        - Each map can contain the following keys:
     *          - "class": Specifies the class name. If not provided, "DefaultClass" will be used as the default name.
     *          - "constants": A map of constant names and values to be included in the class.
     * @param numberIndentSpace (int): The number of spaces to use for indentation in the generated class.
     *
     * @return String: The generated Java class content.
     *
     * @example
     * <pre>{@code
     * // Example 1: Generate a class with default name and constants
     * List<Map<String, Object>> elements = new ArrayList<>();
     * Map<String, Object> constants = new HashMap<>();
     * constants.put("VERSION", "1.0");
     * constants.put("DEBUG_MODE", true);
     * elements.add(Map.of("constants", constants));
     * String javaClass = ClassGenerator.generateClassContent(elements, 4);
     * System.out.println(javaClass);
     * 
     * // Example 2: Generate a custom-named class with constants
     * List<Map<String, Object>> elements = new ArrayList<>();
     * elements.add(Map.of("class", "AppConfig"));
     * Map<String, Object> constants = new HashMap<>();
     * constants.put("MAX_RETRIES", 3);
     * constants.put("APP_NAME", "MyApp");
     * elements.add(Map.of("constants", constants));
     * String javaClass = ClassGenerator.generateClassContent(elements, 4);
     * System.out.println(javaClass);
     * }</pre>
     *
     * @imports
     * java.util.List, java.util.Map
     */
    public static String generateClassContent(List<Map<String, Object>> elements, int numberIndentSpace) {
        String className = "DefaultClass"; // Default class name
        StringBuilder classContent = new StringBuilder();

        // Generate the indent string based on numberIndentSpace
        String indent = " ".repeat(numberIndentSpace);

        // Start parsing the input list
        for (Map<String, Object> element : elements) {
            if (element.containsKey("class")) {
                className = element.get("class").toString();
            }
            if (element.containsKey("constants")) {
                @SuppressWarnings("unchecked")
                Map<String, Object> constants = (Map<String, Object>) element.get("constants");
                for (Map.Entry<String, Object> entry : constants.entrySet()) {
                    // Determine the type based on the value instance
                    String type = entry.getValue() instanceof String ? "String" : "boolean";
                    classContent.append(indent)
                                .append("public static final ")
                                .append(type)
                                .append(" ")
                                .append(entry.getKey())
                                .append(" = ")
                                .append(type.equals("String") ? "\"" + entry.getValue() + "\"" : entry.getValue())
                                .append(";\n");
                }
            }
        }

        // Build the complete class content
        String classTemplate = "public class " + className + " {\n" +
                            classContent.toString() + "}\n";
        return classTemplate;
    }
}
