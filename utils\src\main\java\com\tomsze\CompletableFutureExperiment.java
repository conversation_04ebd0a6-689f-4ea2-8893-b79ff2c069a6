package com.tomsze;

import java.util.Scanner;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

public class CompletableFutureExperiment {
    public static CompletableFuture<String> fetchDataAsync() {
        // Mock async function
        return CompletableFuture.supplyAsync(() -> {
            try {
                Thread.sleep(1000); // Simulate delay
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            return "Async data fetched";
        });
    }

    public static void main(String[] args) {
        try (Scanner scanner = new Scanner(System.in)) {
            System.out.println("Fetching data asynchronously...");
            while (true) {
                // Call the asynchronous function
                CompletableFuture<String> future = fetchDataAsync();

                // Optionally, do other tasks here while waiting for the result

                // Get the result using .thenAccept
                future.thenAccept(result -> {
                    System.out.println("Result from thenAccept: " + result);
                });

                // Wait for the result using get()
                try {
                    String result = future.get(); // This will block until the result is available
                    System.out.println(result);
                } catch (InterruptedException | ExecutionException e) {
                    e.printStackTrace();
                }

                System.out.println("Press E to stop, or any other key to continue...");

                // Check if ESC is pressed
                String input = scanner.nextLine();
                if (input.equalsIgnoreCase("E")) {
                    break;
                }
            }
        }

        System.out.println("Main method finished.");
    }


}


// Note the result:
// User program running
// Fetching data asynchronously...
// Async data fetched
// Press E to stop, or any other key to continue...
// Result from thenAccept: Async data fetched
// E
// Main method finished.
