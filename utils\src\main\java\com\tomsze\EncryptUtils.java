
package com.tomsze;

import java.security.spec.KeySpec;
import java.util.Base64;
import java.util.Map;

import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.PBEKeySpec;

import org.json.JSONObject;

import com.macasaet.fernet.Key;
import com.macasaet.fernet.StringValidator;
import com.macasaet.fernet.Token;
import com.macasaet.fernet.Validator;

import static com.tomsze.APIUtils.apiGet;
import static com.tomsze.APIUtils.apiGetCurrentTimeInZone;
import static com.tomsze.TimeUtils.convertDateString;

public class EncryptUtils {


    /**
     * Encrypts the given data using the specified key.
     *
     * Note: ----- The encrypted data will differ from the original data, which is expected and
     * normal.
     *
     * @param data (byte[]): The data to be encrypted.
     * @param keyBytes (byte[]): The encryption key.
     * @return (String): The encrypted data as a serialized token.
     * 
     * @example
     * 
     *          <pre>{@code
     * import com.tomsze.EncryptUtils;
     * 
     * String encryptedData = EncryptUtils.encryptData("hello world".getBytes(),
     *         "01234567890123456789012345678901".getBytes());
     * }</pre>
     * 
     * @example
     * 
     *          <pre>{@code
     * import com.tomsze.EncryptUtils;
     * 
     * String encryptedData = EncryptUtils.encryptData("another test".getBytes(),
     *         "12345678901234567890123456789012".getBytes());
     * }</pre>
     */
    public static String encryptData(byte[] data, byte[] keyBytes) {
        if (keyBytes.length != 32) {
            throw new IllegalArgumentException("Key must be an array of 32 bytes.");
        }
        Key key = new Key(keyBytes);
        Token token = Token.generate(key, data);

        return token.serialise();
    }

    public static void main(String[] args) {
        String result = encryptData("Hello, World!".getBytes(),
                "01234567890123456789012345678901".getBytes());
        System.out.println(result);

    }



    /**
     * Decrypts the given token using the specified key.
     *
     * Note: - The encrypted data will differ from the original data, which is expected and normal.
     * - The keyBytes must be of length 32.
     *
     * @param keyBytes (byte[]): The encryption key.
     * @param tokenString (String): The token to be decrypted.
     * @param validator (Validator<String>): The validator for the decrypted payload. Defaults to
     *        StringValidator().
     * @return data2 (String): The decrypted data.
     * 
     * @example
     * 
     *          <pre>{@code
     * import com.tomsze.EncryptUtils;
     * 
     * String decryptedData = EncryptUtils.decryptData(
     *         "01234567890123456789012345678901".getBytes(),
     *         "your_token_string_here");
     * }</pre>
     * 
     * @example
     * 
     *          <pre>{@code
     * import com.tomsze.EncryptUtils;
     * 
     * String decryptedData = EncryptUtils.decryptData(
     *         "12345678901234567890123456789012".getBytes(),
     *         "another_token_string_here");
     * }</pre>
     */
    public static String decryptData(byte[] keyBytes, String tokenString,
            Validator<String> validator) {
        if (keyBytes.length != 32) {
            throw new IllegalArgumentException("Key must be an array of 32 bytes.");
        }
        // Deserialize the key
        Key key = new Key(keyBytes);

        // Deserialize the token
        Token token = Token.fromString(tokenString); // Do not use Token.fromBytes()

        // Validate and decrypt the token
        try {
            return token.validateAndDecrypt(key,
                    validator != null ? validator : new StringValidator() {});
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * Generates a key from a given string using PBKDF2 with HMAC SHA-256.
     *
     * @param string (String): The input string to derive the key from.
     * @param salt (byte[]): The salt to be used in the key generation process. Defaults to
     *        "default_salt" if not provided.
     * @param iterations (Integer): The number of iterations for the key derivation. Defaults to
     *        100000 if null.
     * @param length (Integer): The desired length of the generated key in bytes. Defaults to 32 if
     *        not provided.
     * @return (byte[]): A byte array representing the generated key encoded as a URL-safe base64
     *         string. The returned byte array will have a length of 44 bytes.
     * @example
     * 
     *          <pre>{@code
     * import com.tomsze.EncryptUtils;
     * 
     * byte[] salt = "randomSalt".getBytes();
     * byte[] key = EncryptUtils.generateKeyFromString(string: "myPassword", salt: salt, iterations: 100000, length: 32);
     * }</pre>
     * 
     * @example
     * 
     *          <pre>{@code
     * import com.tomsze.EncryptUtils;
     * 
     * byte[] key = EncryptUtils.generateKeyFromString(string: "myPassword", iterations: null, length: 32); // Uses default salt and default iterations
     * }</pre>
     */
    public static byte[] generateKeyFromString(String string, byte[] salt, Integer iterations,
            Integer length) throws Exception {
        if (salt == null) {
            salt = "default_salt".getBytes(); // Default salt
        }
        // Set default iterations if null
        if (iterations == null) {
            iterations = 100000; // Default iterations
        }
        // Set default length if null
        if (length == null) {
            length = 32; // Default length
        }
        // Define the key specification
        KeySpec spec = new PBEKeySpec(string.toCharArray(), salt, iterations, length * 8); // length
                                                                                           // in
                                                                                           // bits

        // Create a SecretKeyFactory for PBKDF2 with HMAC SHA-256
        SecretKeyFactory factory = SecretKeyFactory.getInstance("PBKDF2WithHmacSHA256");

        // Generate the key
        byte[] key = factory.generateSecret(spec).getEncoded();

        // Encode the key to a URL-safe base64 string
        return Base64.getUrlEncoder().encodeToString(key).getBytes();
    }

    /**
     * Generates a key from a dictionary and the current time from an API based on the provided time
     * zone and format.
     *
     * @param dateStringDict (Map<String, String[]>): A map containing date strings for key
     *        generation.
     * @param timeZone (String): The time zone to fetch the current time. Defaults to
     *        "Asia/Hong_Kong" if not provided.
     * @param timeFormat (String): The format for the time string. Defaults to "##yyyy~~MM--dd**" if
     *        not provided.
     * @return (byte[]): A byte array representing the generated key.
     * 
     * @example
     * 
     *          <pre>{@code
     * import com.tomsze.EncryptUtils;
     * import java.util.Map;
     * 
     * Map<String, String[]> dateStringDict = Map.of(
     *     "0123", new String[]{"keyPart1", "keyPart2"}
     * );
     * byte[] key = EncryptUtils.generateKeyFromKeyDictAndApiTimezone(dateStringDict, "Europe/Amsterdam", "yyyy-MM-dd");
     * }</pre>
     * 
     * @example
     * 
     *          <pre>{@code
     * import com.tomsze.EncryptUtils;
     * import java.util.Map;
     * 
     * Map<String, String[]> dateStringDict = Map.of(
     *     "0405", new String[]{"keyPartA", "keyPartB"}
     * );
     * byte[] key = EncryptUtils.generateKeyFromKeyDictAndApiTimezone(dateStringDict, null, null); // Uses default time zone and format
     * }</pre>
     */
    public static byte[] generateKeyFromKeyDictAndApiTimezone(Map<String, String[]> dateStringDict,
            String timeZone, String timeFormat) throws Exception {
        // Set default time zone if none provided
        if (timeZone == null) {
            timeZone = "Asia/Hong_Kong"; // Default time zone
        }

        // Set default time_format if null
        if (timeFormat == null) {
            timeFormat = "##yyyy~~MM--dd**"; // Default time format
        }

        // Fetch current time from API
        JSONObject sysDatetimeDict = apiGetCurrentTimeInZone(timeZone); // might cause Read Timeout
        String sysDatetimeStr = sysDatetimeDict.optString("dateTime", null);

        // Format the datetime string according to the provided time_format
        sysDatetimeStr = sysDatetimeStr.split("\\.")[0]; // Remove the last part that causes problem
        sysDatetimeStr = sysDatetimeStr.split("T")[0]; // Remove the last part that causes problem

        String formattedDatetimeStr = convertDateString(sysDatetimeStr, timeFormat);

        // Combine keys and formatted datetime string
        String month = sysDatetimeDict.optString("month", "");
        String day = sysDatetimeDict.optString("day", "");
        String formatted_month_day = String.format("%02d", Integer.parseInt(month)) + String.format("%02d", Integer.parseInt(day));
        String[] XStrArr = dateStringDict.get(formatted_month_day);
        String key1 = XStrArr[0];
        String key2 = XStrArr[1];
        String baseKeyString = key1 + formattedDatetimeStr + key2;
        return generateKeyFromString(baseKeyString, null, null, null);
    }

    public static byte[] generateKeyFromKeyDictAndServerDatetime(Map<String, String[]> dateStringDict,
            String timeFormat, String urlString) throws Exception {

        // Set default time_format if null
        if (timeFormat == null) {
            timeFormat = "##yyyy~~MM--dd**"; // Default time format
        }

        // Fetch current time from API
        JSONObject sysDatetimeDict = apiGet(urlString);     // might cause Read Timeout
        Integer yearInteger = sysDatetimeDict.getJSONObject("Response").getJSONObject("ApiFunResult").getInt("year");
        Integer monthInteger = sysDatetimeDict.getJSONObject("Response").getJSONObject("ApiFunResult").getInt("month");
        Integer dayInteger = sysDatetimeDict.getJSONObject("Response").getJSONObject("ApiFunResult").getInt("day");
        String sysDatetimeStr = String.format("%04d-%02d-%02d", yearInteger,monthInteger,dayInteger) ;
        String formattedDatetimeStr = convertDateString(sysDatetimeStr, timeFormat);

        // Combine keys and formatted datetime string
        String formatted_month_day = String.format("%02d", monthInteger) + String.format("%02d", dayInteger);
        String[] XStrArr = dateStringDict.get(formatted_month_day);
        String key1 = XStrArr[0];
        String key2 = XStrArr[1];
        String baseKeyString = key1 + formattedDatetimeStr +  key2;
        return generateKeyFromString(baseKeyString, null, null, null);
    }

}
