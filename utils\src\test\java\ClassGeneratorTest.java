import static org.junit.jupiter.api.Assertions.*;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.util.*;

import java.nio.charset.StandardCharsets;

import com.tomsze.ClassGenerator;

public class ClassGeneratorTest {
    @TempDir
    File tempDir;

    @Test
    public void testGenerateClassFileWithClassAndConstants() throws IOException {
        // Prepare input with class name "TestClass" and two constants.
        List<Map<String, Object>> elements = new ArrayList<>();
        
        // Specify the class name.
        Map<String, Object> classInfo = new HashMap<>();
        classInfo.put("class", "TestClass");
        elements.add(classInfo);
        
        // Use a LinkedHashMap to preserve insertion order for testing.
        Map<String, Object> constants = new LinkedHashMap<>();
        constants.put("abc", true);
        constants.put("eee", "eee");
        // Wrap the constants map.
        Map<String, Object> constantsWrapper = new HashMap<>();
        constantsWrapper.put("constants", constants);
        elements.add(constantsWrapper);
        
        // Invoke the function to generate the file.
        ClassGenerator.generateClassFile(elements, 4);

        // The expected file name is based on the class name.
        String fileName = "TestClass.java";
        File file = new File(fileName);
        assertTrue(file.exists(), "The file " + fileName + " should be created.");

        // Read the file contents.
        String content = new String(Files.readAllBytes(file.toPath()));
        String expectedContent =
            "public class TestClass {\n" +
            "    public static final boolean abc = true;\n" +
            "    public static final String eee = \"eee\";\n" +
            "}\n";
        assertEquals(expectedContent, content, "The file content is not as expected.");
        
        // Clean up – delete the generated file.
        file.delete();
    }
    
    @Test
    public void testGenerateClassFileWithoutClassProvided() throws IOException {
        // Prepare input with only constants, no explicit class name.
        List<Map<String, Object>> elements = new ArrayList<>();
        
        // Only constants map is provided. This should use "DefaultClass".
        Map<String, Object> constants = new LinkedHashMap<>();
        constants.put("flag", true);
        Map<String, Object> constantsWrapper = new HashMap<>();
        constantsWrapper.put("constants", constants);
        elements.add(constantsWrapper);
        
        // Invoke the function.
        ClassGenerator.generateClassFile(elements, 4);

        // Default file name "DefaultClass.java" should be created.
        String fileName = "DefaultClass.java";
        File file = new File(fileName);
        assertTrue(file.exists(), "The file " + fileName + " should be created.");

        // Verify the file content.
        String content = new String(Files.readAllBytes(file.toPath()));
        String expectedContent =
            "public class DefaultClass {\n" +
            "    public static final boolean flag = true;\n" +
            "}\n";
        assertEquals(expectedContent, content, "The file content for DefaultClass is not as expected.");

        file.delete();
    }

    // --------------------------

    /**
     * Test the basic functionality of generateEnum(). 
     * Creates an input file with three enum constants and verifies that the generated enum file exists
     * and its content exactly matches the expected output.
     */
    @Test
    void testGenerateEnum_normalInput() throws IOException {
        // Create a temporary input file with simple enum values.
        File inputFile = new File(tempDir, "enum.txt");
        String inputContent = "red\nblue\ngreen";
        Files.write(inputFile.toPath(), inputContent.getBytes(StandardCharsets.UTF_8));

        // Setup parameters.
        String packageName = "com.example.generated";
        String enumName = "MyEnum";
        // Create output directory according to the package structure.
        File outputDir = new File(tempDir, packageName.replace('.', File.separatorChar));

        // Call the generation method.
        ClassGenerator.generateEnum(inputFile, outputDir, packageName, enumName);

        // Verify that the generated file exists.
        File generatedFile = new File(outputDir, enumName + ".java");
        assertTrue(generatedFile.exists(), "Generated enum file should exist.");

        // Expected enum file content.
        String expectedContent = 
            "package " + packageName + ";\n\n" +
            "public enum " + enumName + " {\n" +
            "    RED,\n" +
            "    BLUE,\n" +
            "    GREEN;\n" +
            "}\n";

        // Read the generated file and then compare.
        String actualContent = Files.readString(generatedFile.toPath(), StandardCharsets.UTF_8);
        assertEquals(expectedContent, actualContent, "Generated file content must match expected output.");
    }

    /**
     * This test verifies that generateEnum() properly handles extra whitespace and empty lines.
     * It ensures that blank lines are ignored and that values get trimmed.
     */
    @Test
    void testGenerateEnum_inputWithExtraWhitespace() throws IOException {
        // Create temporary input file with extra spaces and empty lines.
        File inputFile = new File(tempDir, "enum.txt");
        String inputContent = "\n   red   \n\nblue \n    green  \n";
        Files.write(inputFile.toPath(), inputContent.getBytes(StandardCharsets.UTF_8));

        String packageName = "com.example.generated";
        String enumName = "MyEnum";
        File outputDir = new File(tempDir, packageName.replace('.', File.separatorChar));

        // Run the enum generation.
        ClassGenerator.generateEnum(inputFile, outputDir, packageName, enumName);

        // Confirm that the file is created.
        File generatedFile = new File(outputDir, enumName + ".java");
        assertTrue(generatedFile.exists(), "Generated enum file should exist, regardless of extra whitespace.");

        String expectedContent =
            "package " + packageName + ";\n\n" +
            "public enum " + enumName + " {\n" +
            "    RED,\n" +
            "    BLUE,\n" +
            "    GREEN;\n" +
            "}\n";

        String actualContent = Files.readString(generatedFile.toPath(), StandardCharsets.UTF_8);
        assertEquals(expectedContent, actualContent, "Whitespace should be properly trimmed in the generated enum.");
    }
    // ---------------------------------------

    @Test
    public void testEmptyElements() {
        // When no elements are provided, we expect the default class with no constants.
        List<Map<String, Object>> elements = new ArrayList<>();
        String expected = "public class DefaultClass {\n}\n";
        String actual = ClassGenerator.generateClassContent(elements, 4);
        assertEquals(expected, actual);
    }

    @Test
    public void testDefaultClassWithConstants() {
        // Test the case where constants are provided and no specific class name is given.
        List<Map<String, Object>> elements = new ArrayList<>();
        
        // Using a LinkedHashMap to preserve insertion order for testing.
        Map<String, Object> constants = new LinkedHashMap<>();
        constants.put("VERSION", "1.0");
        constants.put("DEBUG_MODE", true);
        
        // Add the constants map to the elements list.
        elements.add(Collections.singletonMap("constants", constants));
        
        String expected = "public class DefaultClass {\n" +
                          "    public static final String VERSION = \"1.0\";\n" +
                          "    public static final boolean DEBUG_MODE = true;\n" +
                          "}\n";
        String actual = ClassGenerator.generateClassContent(elements, 4);
        assertEquals(expected, actual);
    }

    @Test
    public void testCustomClassNameWithConstants() {
        // Test the scenario where a custom class name is provided along with constants.
        List<Map<String, Object>> elements = new ArrayList<>();
        
        // First, add an element specifying a custom class name.
        elements.add(Collections.singletonMap("class", "AppConfig"));
        
        // Now add an element for constants.
        Map<String, Object> constants = new LinkedHashMap<>();
        // Note: For non-string values (like 3), the logic sets the type to "boolean" as per the function.
        constants.put("MAX_RETRIES", 3);
        constants.put("APP_NAME", "MyApp");
        elements.add(Collections.singletonMap("constants", constants));
        
        String expected = "public class AppConfig {\n" +
                          "    public static final boolean MAX_RETRIES = 3;\n" +
                          "    public static final String APP_NAME = \"MyApp\";\n" +
                          "}\n";
        String actual = ClassGenerator.generateClassContent(elements, 4);
        assertEquals(expected, actual);
    }

}
