import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;
import org.junit.jupiter.api.Nested;

import static org.junit.jupiter.api.Assertions.*;
import com.tomsze.PythonToJavaConverter;

import java.nio.file.Path;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.io.IOException;
import java.io.File;

public class PythonToJavaConverterTest {

    @Test
    public void testConvertPythonScript() {
        // Arrange
        String pythonScript = 
              "class Constants:\n" +
              "    PI = 3.14\n" +
              "    E = 2.71\n" +
              "\n" +
              "class Greetings:\n" +
              "    HELLO = 'Hello, World!'\n";
              
        // Act
        String javaOutput = PythonToJavaConverter.convertPythonScript(pythonScript);
        
        // Assert
        assertNotNull("The output should not be null", javaOutput);
        
        // Check that the generated code contains the expected class definitions.
        assertTrue(javaOutput.contains("public class Constants {"),
                   "Output should contain the 'Constants' class definition");
        assertTrue(javaOutput.contains("public class Greetings {"),
                   "Output should contain the 'Greetings' class definition");
        
        // Check that the generated code contains the expected constant declarations.
        assertTrue(javaOutput.contains("public static final double PI = 3.14;"),
                   "Output should contain constant declaration for PI as a double");
        assertTrue(javaOutput.contains("public static final double E = 2.71;"),
                   "Output should contain constant declaration for E as a double");
        assertTrue(javaOutput.contains("public static final String HELLO = \"Hello, World!\";"),
                   "Output should contain constant declaration for HELLO as a String");
    }

    
    @Nested
    public class ConvertPythonScriptAndWriteToFileTest
    {
        @TempDir
        Path tempDir;

        @Test
        public void testConvertPythonScriptAndWriteToFile_AddsScriptNameToPackage() throws IOException {
            // Arrange
            Path script = Files.createTempFile(tempDir, "myscript", ".py");
            String filename = script.getFileName().toString().replace(".py", "");

            String python = "class Foo:\n    BAR = 123\n";
            Files.write(script, python.getBytes(StandardCharsets.UTF_8));
            String belongToPackage = "com.example.pkg";

            // Act
            PythonToJavaConverter.convertPythonScriptAndWriteToFile(
                script.toString(),
                tempDir.toString(),
                belongToPackage,
                true
            );

            // Assert
            File fooFile = new File(tempDir.toFile() + File.separator + filename + File.separator, "Foo.java");
            assertTrue(fooFile.exists(), "Foo.java should be generated");
            String content = new String(Files.readAllBytes(fooFile.toPath()), StandardCharsets.UTF_8);
            assertTrue(content.contains("package com.example.pkg.myscript"),
            "Package should include script name when addPythonScriptNameToPackageName is true");
            assertTrue(content.contains("public static final int BAR = 123;"),
            "Should generate correct constant");
        }

        @Test
        public void testConvertPythonScriptAndWriteToFile_DoesNotAddScriptNameToPackage() throws IOException {
            // Arrange
            Path script = Files.createTempFile(tempDir, "otherscript", ".py");
            String python = "class Bar:\n    BAZ = 'baz'\n";
            Files.write(script, python.getBytes(StandardCharsets.UTF_8));
            String belongToPackage = "com.example.base";

            // Act
            PythonToJavaConverter.convertPythonScriptAndWriteToFile(
            script.toString(),
            tempDir.toString(),
            belongToPackage,
            false
            );

            // Assert
            File barFile = new File(tempDir.toFile(), "Bar.java");
            assertTrue(barFile.exists(), "Bar.java should be generated");
            String content = new String(Files.readAllBytes(barFile.toPath()), StandardCharsets.UTF_8);
            assertTrue(content.contains("package com.example.base;"),
            "Package should not include script name when addPythonScriptNameToPackageName is false");
            assertTrue(content.contains("public static final String BAZ = \"baz\";"),
            "Should generate correct constant");
        }

        @Test
        public void testConvertPythonScriptAndWriteToFile_EmptyPackageName() throws IOException {
            // Arrange
            Path script = Files.createTempFile(tempDir, "empty", ".py");
            String python = "class Empty:\n    VALUE = 0\n";
            Files.write(script, python.getBytes(StandardCharsets.UTF_8));
            String belongToPackage = "";

            // Act
            PythonToJavaConverter.convertPythonScriptAndWriteToFile(
            script.toString(),
            tempDir.toString(),
            belongToPackage,
            false
            );

            // Assert
            File emptyFile = new File(tempDir.toFile(), "Empty.java");
            assertTrue(emptyFile.exists(), "Empty.java should be generated");
            String content = new String(Files.readAllBytes(emptyFile.toPath()), StandardCharsets.UTF_8);
            assertFalse(content.contains("package"),
            "Should not generate a package statement when belongToPackage is empty");
            assertTrue(content.contains("public static final int VALUE = 0;"),
            "Should generate correct constant");
        }

        @Test
        public void testConvertPythonScriptAndWriteToFile_CreatesDirectoryIfMissing() throws IOException {
            // Arrange
            File newDir = new File(tempDir.toFile(), "deepdir");
            assertFalse(newDir.exists(), "Directory should not exist before test");
            Path script = Files.createTempFile(tempDir, "deep", ".py");
            String python = "class Deep:\n    D = 9\n";
            Files.write(script, python.getBytes(StandardCharsets.UTF_8));

            // Act
            PythonToJavaConverter.convertPythonScriptAndWriteToFile(
            script.toString(),
            newDir.getAbsolutePath(),
            "com.deep.test",
            false
            );

            // Assert
            assertTrue(newDir.exists(), "Directory should be created");
            File deepFile = new File(newDir, "Deep.java");
            assertTrue(deepFile.exists(), "Deep.java should be generated in new directory");
            String content = new String(Files.readAllBytes(deepFile.toPath()), StandardCharsets.UTF_8);
            assertTrue(content.contains("public static final int D = 9;"),
            "Should generate correct constant");
        }

        @Test
        public void testConvertPythonScriptAndWriteToFile_MultipleClasses() throws IOException {
            // Arrange
            Path script = Files.createTempFile(tempDir, "multi", ".py");
            String python = "class First:\n    X = 1\nclass Second:\n    Y = 'y'\n";
            Files.write(script, python.getBytes(StandardCharsets.UTF_8));
            String belongToPackage = "com.example.multi";

            // Act
            PythonToJavaConverter.convertPythonScriptAndWriteToFile(
            script.toString(),
            tempDir.toString(),
            belongToPackage,
            false
            );

            // Assert
            File firstFile = new File(tempDir.toFile(), "First.java");
            File secondFile = new File(tempDir.toFile(), "Second.java");
            assertTrue(firstFile.exists(), "First.java should be generated");
            assertTrue(secondFile.exists(), "Second.java should be generated");
            String firstContent = new String(Files.readAllBytes(firstFile.toPath()), StandardCharsets.UTF_8);
            String secondContent = new String(Files.readAllBytes(secondFile.toPath()), StandardCharsets.UTF_8);
            assertTrue(firstContent.contains("public static final int X = 1;"), "First.java should have constant X");
            assertTrue(secondContent.contains("public static final String Y = \"y\";"), "Second.java should have constant Y");
        }
    }

    @Nested
    public class GenerateJavaConstantDeclarationTest {
    
        @Test
        public void testGenerateJavaConstantDeclaration_StringType() {
            PythonToJavaConverter.Constant constant = new PythonToJavaConverter.Constant("GREETING", "'Hello!'", "String");
            String result = PythonToJavaConverter.generateJavaConstantDeclaration(constant, 4);
            assertTrue(result.contains("public static final String GREETING = \"Hello!\";"),
                    "Should generate correct String constant declaration with double quotes");
            assertTrue(result.startsWith("    "), "Should have 4 spaces indentation");
        }
    
        @Test
        public void testGenerateJavaConstantDeclaration_StringTypeWithDoubleQuotes() {
            PythonToJavaConverter.Constant constant = new PythonToJavaConverter.Constant("MESSAGE", "\"Hi!\"", "String");
            String result = PythonToJavaConverter.generateJavaConstantDeclaration(constant, 2);
            assertTrue(result.contains("public static final String MESSAGE = \"Hi!\";"),
                    "Should handle already double-quoted string values");
            assertTrue(result.startsWith("  "), "Should have 2 spaces indentation");
        }
    
        @Test
        public void testGenerateJavaConstantDeclaration_IntType() {
            PythonToJavaConverter.Constant constant = new PythonToJavaConverter.Constant("ANSWER", "42", "int");
            String result = PythonToJavaConverter.generateJavaConstantDeclaration(constant, 0);
            assertEquals("public static final int ANSWER = 42;\n", result,
                    "Should generate correct int constant declaration with no indentation");
        }
    
        @Test
        public void testGenerateJavaConstantDeclaration_DoubleType() {
            PythonToJavaConverter.Constant constant = new PythonToJavaConverter.Constant("PI", "3.1415", "double");
            String result = PythonToJavaConverter.generateJavaConstantDeclaration(constant, 3);
            assertTrue(result.contains("public static final double PI = 3.1415;"),
                    "Should generate correct double constant declaration");
            assertTrue(result.startsWith("   "), "Should have 3 spaces indentation");
        }
    
        @Test
        public void testGenerateJavaConstantDeclaration_StringTypeWithoutQuotes() {
            PythonToJavaConverter.Constant constant = new PythonToJavaConverter.Constant("NAME", "World", "String");
            String result = PythonToJavaConverter.generateJavaConstantDeclaration(constant, 1);
            assertTrue(result.contains("public static final String NAME = \"World\";"),
                    "Should wrap unquoted string values in double quotes");
            assertTrue(result.startsWith(" "), "Should have 1 space indentation");
        }

    }


    @Nested
    public class ConvertPythonScriptsAndWriteToFileTest {
        @TempDir
        Path tempDir;
        @Test
        public void testConvertPythonScriptsAndWriteToFile_MultipleScripts() throws IOException {
            // Arrange: create two temporary Python scripts
            Path script1 = Files.createTempFile(tempDir, "script1", ".py");
            Path script2 = Files.createTempFile(tempDir, "script2", ".py");
            String filename1 = script1.getFileName().toString().replace(".py", "");
            String filename2 = script2.getFileName().toString().replace(".py", "");
            String python1 = "class Alpha:\n    A = 1\n";
            String python2 = "class Beta:\n    B = 'bee'\n";
            Files.write(script1, python1.getBytes(StandardCharsets.UTF_8));
            Files.write(script2, python2.getBytes(StandardCharsets.UTF_8));
            String belongToPackage = "com.example.multiple";

            // Act
            PythonToJavaConverter.convertPythonScriptsAndWriteToFile(
            java.util.Arrays.asList(script1.toString(), script2.toString()),
            tempDir.toString(),
            belongToPackage,
            true
            );

            // Assert: both Java files should exist and have correct content
            File alphaFile = new File(tempDir.toFile()+ File.separator + filename1 + File.separator, "Alpha.java");
            File betaFile = new File(tempDir.toFile()+  File.separator + filename2 + File.separator, "Beta.java");
            assertTrue(alphaFile.exists(), "Alpha.java should be generated");
            assertTrue(betaFile.exists(), "Beta.java should be generated");

            String alphaContent = new String(Files.readAllBytes(alphaFile.toPath()), StandardCharsets.UTF_8);
            String betaContent = new String(Files.readAllBytes(betaFile.toPath()), StandardCharsets.UTF_8);

            assertTrue(alphaContent.contains("package " + belongToPackage), "Alpha.java should have package declaration");
            assertTrue(alphaContent.contains("public static final int A = 1;"), "Alpha.java should have constant A");

            assertTrue(betaContent.contains("package " + belongToPackage), "Beta.java should have package declaration");
            assertTrue(betaContent.contains("public static final String B = \"bee\";"), "Beta.java should have constant B");
        }

        @Test
        public void testConvertPythonScriptsAndWriteToFile_CreatesDirectoryIfNotExists() throws IOException {
            // Arrange: create a new subdirectory that does not exist yet
            File newDir = new File(tempDir.toFile(), "subdir");
            assertFalse(newDir.exists(), "Subdirectory should not exist before test");
            Path script = Files.createTempFile(tempDir, "script", ".py");
            String filename = script.getFileName().toString().replace(".py", "");
            String python = "class Gamma:\n    G = 7\n";
            Files.write(script, python.getBytes(StandardCharsets.UTF_8));

            // Act
            PythonToJavaConverter.convertPythonScriptsAndWriteToFile(
            java.util.Collections.singletonList(script.toString()),
            newDir.getAbsolutePath(),
            "com.example.dir",
            true
            );

            // Assert
            assertTrue(newDir.exists(), "Subdirectory should be created");
            File gammaFile = new File(newDir + File.separator + filename + File.separator, "Gamma.java");
            assertTrue(gammaFile.exists(), "Gamma.java should be generated in new subdirectory");
            String content = new String(Files.readAllBytes(gammaFile.toPath()), StandardCharsets.UTF_8);
            assertTrue(content.contains("public static final int G = 7;"), "Gamma.java should have constant G");
        }

        @Nested
        public class ExtractConstantNameTest {

            @Test
            public void testExtractConstantName_SimpleAssignment() {
                String line = "FOO = 123";
                String name = invokeExtractConstantName(line);
                assertEquals("FOO", name, "Should extract simple constant name");
            }

            @Test
            public void testExtractConstantName_WithTypeAnnotation() {
                String line = "BAR :str = 'baz'";
                String name = invokeExtractConstantName(line);
                assertEquals("BAR", name, "Should trim spaces around the name");
            }

            @Test
            public void testExtractConstantName_WithSpaces() {
                String line = "   BAR   =   'baz'";
                String name = invokeExtractConstantName(line);
                assertEquals("BAR", name, "Should trim spaces around the name");
            }

            @Test
            public void testExtractConstantName_WithTabs() {
                String line = "\tHELLO\t= \"world\"";
                String name = invokeExtractConstantName(line);
                assertEquals("HELLO", name, "Should trim tabs and spaces");
            }

            @Test
            public void testExtractConstantName_WithUnderscores() {
                String line = "MY_CONST_1 = 42";
                String name = invokeExtractConstantName(line);
                assertEquals("MY_CONST_1", name, "Should extract name with underscores and digits");
            }

            @Test
            public void testExtractConstantName_ExtraEqualsInValue() {
                String line = "TOKEN = 'a=b=c'";
                String name = invokeExtractConstantName(line);
                assertEquals("TOKEN", name, "Should only split at the first equals sign");
            }

            @Test
            public void testExtractConstantName_LeadingIndentation() {
                String line = "    INDENTED = 5";
                String name = invokeExtractConstantName(line);
                assertEquals("INDENTED", name, "Should ignore leading indentation");
            }


            // Helper to invoke the private static method using reflection
            private String invokeExtractConstantName(String line) {
                try {
                    java.lang.reflect.Method m = PythonToJavaConverter.class.getDeclaredMethod("extractConstantName", String.class);
                    m.setAccessible(true);
                    return (String) m.invoke(null, line);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            }
        }
    }


    @Nested
    public class IsConstantDefinitionTest {

        // Helper to invoke the private static method using reflection
        private boolean invokeIsConstantDefinition(String line) {
            try {
                java.lang.reflect.Method m = PythonToJavaConverter.class.getDeclaredMethod("isConstantDefinition", String.class);
                m.setAccessible(true);
                return (boolean) m.invoke(null, line);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }

        @Test
        public void testIsConstantDefinition_UppercaseAssignment() {
            assertTrue(invokeIsConstantDefinition("FOO = 1"), "Should detect uppercase constant assignment");
        }

        @Test
        public void testIsConstantDefinition_WithTypeAnnotation() {
            assertTrue(invokeIsConstantDefinition("BAR: int = 2"), "Should detect constant with type annotation");
        }

        @Test
        public void testIsConstantDefinition_WithSpacesAroundEquals() {
            assertTrue(invokeIsConstantDefinition("BAZ   =    3"), "Should detect constant with spaces around equals");
        }

        @Test
        public void testIsConstantDefinition_WithUnderscoresAndDigits() {
            assertTrue(invokeIsConstantDefinition("MY_CONST_1 = 42"), "Should detect constant with underscores and digits");
        }

        @Test
        public void testIsConstantDefinition_WithLeadingIndentation() {
            assertTrue(invokeIsConstantDefinition("    INDENTED = 5"), "Should detect constant with indentation");
        }

        @Test
        public void testIsConstantDefinition_WithTabs() {
            assertTrue(invokeIsConstantDefinition("\tHELLO = \"world\""), "Should detect constant with tabs");
        }

        @Test
        public void testIsConstantDefinition_LowercaseName() {
            assertFalse(invokeIsConstantDefinition("foo = 1"), "Should not detect lowercase variable as constant");
        }

        @Test
        public void testIsConstantDefinition_MixedCaseName() {
            assertFalse(invokeIsConstantDefinition("FooBar = 1"), "Should not detect mixed case variable as constant");
        }

        @Test
        public void testIsConstantDefinition_FunctionDefinition() {
            assertFalse(invokeIsConstantDefinition("def func(): pass"), "Should not detect function definition as constant");
        }

        @Test
        public void testIsConstantDefinition_CommentLine() {
            assertFalse(invokeIsConstantDefinition("# COMMENT = 1"), "Should not detect comment as constant");
        }

        @Test
        public void testIsConstantDefinition_EmptyLine() {
            assertFalse(invokeIsConstantDefinition(""), "Should not detect empty line as constant");
        }

        @Test
        public void testIsConstantDefinition_OnlyEquals() {
            assertFalse(invokeIsConstantDefinition("= 123"), "Should not detect line starting with equals as constant");
        }

        @Test
        public void testIsConstantDefinition_ColonButNoEquals() {
            assertFalse(invokeIsConstantDefinition("FOO: int"), "Should not detect line with colon but no equals as constant");
        }

        @Test
        public void testIsConstantDefinition_UppercaseWithTypeAndSpaces() {
            assertTrue(invokeIsConstantDefinition("FOO_BAR : str = 'abc'"), "Should detect constant with type and spaces");
        }
    }
    
}
